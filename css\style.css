/* ===== CSS VARIABLES (Easy to customize colors) ===== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e74c3c;
    --accent-color: #f39c12;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --text-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 5px 20px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: #34495e;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: var(--bg-white);
    box-shadow: var(--shadow);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 3px 0;
    transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-white) 100%);
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin-right: 2rem;
}

.hero-title {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: var(--shadow-hover);
}

/* ===== SECTION STYLES ===== */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
    background-color: var(--bg-light);
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid var(--border-color);
    background-color: var(--bg-white);
    color: var(--text-dark);
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    cursor: pointer;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.portfolio-item img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--text-white);
    padding: 2rem;
    transform: translateY(100%);
    transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h3 {
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.portfolio-overlay p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* ===== ABOUT SECTION ===== */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.stat p {
    color: var(--text-light);
    font-weight: 500;
    margin: 0;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

/* ===== SERVICES SECTION ===== */
.services {
    background-color: var(--bg-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background-color: var(--bg-white);
    padding: 2.5rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--text-white);
}

.service-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.service-card p {
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.service-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== CONTACT SECTION ===== */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: var(--text-white);
    font-size: 1.2rem;
}

.contact-details h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-details p {
    margin: 0;
    color: var(--text-light);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 45px;
    height: 45px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Contact Form */
.contact-form {
    background-color: var(--bg-light);
    padding: 2.5rem;
    border-radius: 10px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
    background-color: var(--bg-white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-text p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-link:hover {
    color: var(--text-white);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-white);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Hero Section */
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 20px 60px;
    }

    .hero-content {
        margin-right: 0;
        margin-bottom: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    /* Typography */
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }

    /* About Section */
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .about-stats {
        justify-content: center;
    }

    /* Contact Section */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Portfolio Grid */
    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    /* Services Grid */
    .services-grid {
        grid-template-columns: 1fr;
    }

    /* Footer */
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    section {
        padding: 60px 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .portfolio-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== SMOOTH SCROLLING OFFSET ===== */
html {
    scroll-padding-top: 80px;
}
