# ✅ WEBSITE TEMPLATE BUILD SPEC

**Purpose:**  
Use this checklist every time you (or your AI) create a new HTML website template to sell.  
Stick to these rules for consistent, easy-to-sell, beginner-friendly templates.

---

## 📌 1️⃣ Template Name
- Clear, niche-friendly.  
  Example: *“Minimal Portfolio Template for Freelancers”*

---

## 🎯 2️⃣ Target Audience
- Who is this for?  
  Examples: Freelancers, photographers, cafés, artists, small businesses, newsletter landing page, etc.

---

## 📁 3️⃣ File Structure

Your final template should have:

/[Template Folder]
├── index.html
├── /css/
│ └── style.css
├── /js/
│ └── script.js (optional)
├── /images/
│ └── placeholder images
└── README.md (explains how to use)


---

## ✨ 4️⃣ Must-Have Features

- ✅ Modern, clean design
- ✅ Fully responsive (desktop, tablet, mobile)
- ✅ Simple HTML & CSS only (vanilla JS only if needed)
- ✅ Easy for buyer to edit text, images & colours
- ✅ Good performance (fast, lightweight)
- ✅ Accessible (alt text, good contrast, readable fonts)

---

## 📝 5️⃣ Content

- Use realistic dummy text (like Lorem Ipsum or short, human-like lines)
- Use clear placeholder images — name them clearly (`hero.jpg`, `profile.jpg`, `logo.png`)

---

## 🎨 6️⃣ Style Rules

- Modern Google Font (open source)
- Simple colour scheme — easy to find & change in `style.css`
- Basic hover states for buttons & links
- If using animations, keep them simple & smooth

---

## 📄 7️⃣ README.md Requirements

Every template **must** include a `README.md` with:
- ✔️ File structure explained
- ✔️ How to edit text, images, colours
- ✔️ How to upload to a live site (Netlify, GitHub Pages, etc.)
- ✔️ Licence: “Personal/commercial use. Do not resell.”
- ✔️ Contact info for buyer support (or placeholder)

---

## 🌐 8️⃣ Live Demo Note

Include a note for yourself:  
✅ “Upload the finished template to Netlify or GitHub Pages for a live preview link.”

---

## 📦 9️⃣ Delivery

- Deliver final template as a **ZIP-ready folder**.
- Test in browser — no broken links or missing images.
- Check for mobile responsiveness.
- Keep files neat & well-commented if helpful.

---

## ⚡ 1️⃣0️⃣ Example AI Prompt

Use this with your AI buddy:

Hi Augment, please create a responsive HTML website template for [TARGET AUDIENCE].
✔️ Use index.html, css/style.css, optional js/script.js, images folder
✔️ Make it fully responsive, modern, clean, easy to edit
✔️ Add dummy text & placeholder images
✔️ Include a clear README.md covering usage, editing, deploy steps, licence
✔️ Deliver as a clean, zipped project folder
✔️ Keep code simple, no heavy frameworks


---

## ✅ Optional Add-Ons

- Add `contact.html` if you want a simple contact form.
- Add `blog.html` or `portfolio.html` if relevant.
- Offer a light mode & dark mode version for extra value.

---
