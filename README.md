# 📸 Professional Photography Website Template

A modern, responsive HTML website template designed specifically for professional photographers. Perfect for showcasing your portfolio, services, and attracting new clients.

## ✨ Features

- **Fully Responsive** - Looks great on desktop, tablet, and mobile devices
- **Modern Design** - Clean, professional aesthetic that puts your work first
- **Portfolio Gallery** - Filterable portfolio with lightbox functionality
- **Service Showcase** - Dedicated section for your photography packages
- **Contact Form** - Built-in contact form for client inquiries
- **Easy to Customize** - Simple HTML/CSS structure, no complex frameworks
- **Fast Loading** - Optimized for performance and SEO
- **Accessible** - Built with accessibility best practices

## 📁 File Structure

```
photography-template/
├── index.html          # Main website file
├── css/
│   └── style.css      # All styling and responsive design
├── js/
│   └── script.js      # Interactive functionality
├── images/
│   ├── hero.jpg       # Main hero image
│   ├── profile.jpg    # Your professional photo
│   ├── wedding-1.jpg  # Portfolio sample
│   ├── wedding-2.jpg  # Portfolio sample
│   ├── portrait-1.jpg # Portfolio sample
│   ├── portrait-2.jpg # Portfolio sample
│   ├── lifestyle-1.jpg# Portfolio sample
│   └── lifestyle-2.jpg# Portfolio sample
└── README.md          # This file
```

## 🎨 Customization Guide

### 1. Personal Information
**File:** `index.html`

Replace the following placeholder content:
- **Name:** Change "Alex Morgan" to your name (lines 15, 25, 130)
- **Title:** Update the page title and meta description (lines 5-6)
- **Contact Info:** Update email, phone, and location (lines 220-240)
- **About Text:** Replace the about section content (lines 140-155)
- **Services:** Modify service descriptions and pricing (lines 180-210)

### 2. Colors & Styling
**File:** `css/style.css`

The color scheme is defined in CSS variables at the top of the file (lines 2-15):

```css
:root {
    --primary-color: #2c3e50;    /* Main brand color */
    --secondary-color: #e74c3c;  /* Accent color */
    --accent-color: #f39c12;     /* Highlight color */
    /* ... more colors */
}
```

**Easy Color Changes:**
- Change `--primary-color` for your main brand color
- Change `--secondary-color` for buttons and accents
- All colors will automatically update throughout the site

### 3. Images
**Folder:** `images/`

Replace placeholder images with your own:
1. **hero.jpg** (1200x800px) - Main hero section image
2. **profile.jpg** (600x600px) - Your professional headshot
3. **Portfolio images** (800x600px recommended) - Your best work samples

**Image Guidelines:**
- Use high-quality, professional photos
- Optimize for web (compress to under 500KB each)
- Maintain consistent style and color grading
- Use descriptive alt text for accessibility

### 4. Portfolio Categories
**File:** `index.html` (lines 60-65) and `js/script.js` (lines 25-50)

Current categories: Wedding, Portrait, Lifestyle

**To add/change categories:**
1. Update filter buttons in HTML
2. Update portfolio items' `data-category` attributes
3. JavaScript will automatically handle the filtering

### 5. Fonts
**Current fonts:** Playfair Display (headings) + Inter (body text)

**To change fonts:**
1. Update Google Fonts link in `index.html` (line 10)
2. Update font-family in `css/style.css` (lines 35-40)

## 🌐 Deployment Guide

### Option 1: Netlify (Recommended - Free)
1. Create account at [netlify.com](https://netlify.com)
2. Drag and drop your template folder to Netlify
3. Your site will be live instantly with a custom URL
4. Optional: Connect your own domain

### Option 2: GitHub Pages (Free)
1. Create GitHub account and new repository
2. Upload all template files to the repository
3. Go to Settings > Pages
4. Select "Deploy from a branch" and choose "main"
5. Your site will be available at `username.github.io/repository-name`

### Option 3: Traditional Web Hosting
1. Purchase hosting from providers like Bluehost, SiteGround, etc.
2. Upload files via FTP to your hosting account
3. Point your domain to the hosting server

## 📱 Mobile Responsiveness

The template is fully responsive and includes:
- Mobile-friendly navigation menu
- Optimized layouts for tablets and phones
- Touch-friendly buttons and interactions
- Readable text sizes on all devices

**Breakpoints:**
- Desktop: 1200px+
- Tablet: 768px - 1199px
- Mobile: 320px - 767px

## 🔧 Technical Details

**Built with:**
- HTML5 semantic markup
- CSS3 with Flexbox and Grid
- Vanilla JavaScript (no jQuery required)
- Font Awesome icons
- Google Fonts

**Browser Support:**
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## 📞 Support & Customization

**Need help customizing your template?**
- Basic HTML/CSS knowledge recommended
- Refer to inline comments in the code
- Test changes in a web browser
- Use browser developer tools for debugging

**Common Customizations:**
- Adding new pages (duplicate index.html structure)
- Changing layout sections (modify HTML structure)
- Adding animations (CSS transitions included)
- Integrating contact forms with backend services

## 📄 License

**Personal and Commercial Use Permitted**
- Use for your own photography business
- Modify and customize as needed
- Create websites for clients

**Restrictions:**
- Do not resell this template as-is
- Do not redistribute the template files
- Attribution appreciated but not required

## 🚀 Getting Started Checklist

- [ ] Replace all placeholder text with your information
- [ ] Upload your professional photos
- [ ] Customize colors to match your brand
- [ ] Test on mobile devices
- [ ] Update contact information and social links
- [ ] Deploy to your chosen hosting platform
- [ ] Test contact form functionality
- [ ] Set up Google Analytics (optional)
- [ ] Submit to search engines

## 📈 SEO Tips

- Update meta descriptions and titles
- Use descriptive alt text for all images
- Add structured data for local business
- Create XML sitemap
- Optimize images for faster loading
- Use relevant keywords naturally in content

---

**Template Version:** 1.0  
**Last Updated:** 2024  
**Compatible:** All modern browsers  

For questions or support, please refer to the documentation above or consult with a web developer familiar with HTML/CSS.
